#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号管理组件
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QComboBox, QLineEdit, QLabel, QFileDialog, QMessageBox,
    QHeaderView, QMenu, QProgressDialog, QGroupBox, QSpinBox, QSplitter, QDialog
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QAction, QColor, QFont
from typing import List

from src.core.account_manager import AccountManager
from src.database.models import Account, AccountGroup
from src.utils.logger import LoggerMixin
from src.config.constants import ACCOUNT_STATUS
from src.ui.dialogs.group_management_dialog import GroupManagementDialog
from src.ui.dialogs.account_import_dialog import AccountImportDialog
from src.ui.dialogs.account_edit_dialog import AccountEditDialog


class AccountStatusCheckThread(QThread):
    """账号状态检测线程"""
    
    progress_updated = pyqtSignal(int, int, str, str)
    finished = pyqtSignal(list)
    
    def __init__(self, accounts):
        super().__init__()
        self.accounts = accounts
        self.running = True
        self.account_manager = AccountManager()
    
    def run(self):
        """执行状态检测"""
        import asyncio
        from src.core.account_status_manager import AccountStatusManager
        
        results = []
        total = len(self.accounts)
        
        # 创建异步事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            status_manager = AccountStatusManager()
            
            for i, account in enumerate(self.accounts):
                if not self.running:
                    break
                    
                try:
                    self.progress_updated.emit(i, total, account.username, "检测中")
                    
                    # 异步检测账号状态
                    status = loop.run_until_complete(status_manager.check_account_status(account))
                    
                    # 更新数据库中的账号状态
                    self.account_manager.update_account_status(account.id, status)
                    
                    results.append({
                        'account_id': account.id,
                        'username': account.username,
                        'success': True,
                        'status': status
                    })
                    
                except Exception as e:
                    results.append({
                        'account_id': account.id,
                        'username': account.username,
                        'success': False,
                        'error': str(e)
                    })
        finally:
            loop.close()
        
        self.finished.emit(results)
    
    def stop(self):
        """停止线程"""
        self.running = False


class AccountLoginThread(QThread):
    """账号登录线程"""
    
    progress_updated = pyqtSignal(int, int, str, str)
    finished = pyqtSignal(list)
    
    def __init__(self, accounts):
        super().__init__()
        self.accounts = accounts
        self.running = True
        self.account_manager = AccountManager()
    
    def run(self):
        """执行登录"""
        import asyncio
        from src.modules.posting.executor import LoginManager
        from src.core.browser_manager import get_browser_pool
        
        results = []
        total = len(self.accounts)
        
        # 创建异步事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 使用LoginManager进行登录
            async def login_accounts():
                # 初始化浏览器池
                browser_pool = get_browser_pool()
                await browser_pool.initialize()
                
                login_manager = LoginManager()
                
                for i, account in enumerate(self.accounts):
                    if not self.running:
                        break
                        
                    try:
                        self.progress_updated.emit(i, total, account.username, "登录中")
                        
                        # 执行登录 - LoginManager会自动管理浏览器和页面
                        success = await login_manager.login_account(account)
                        
                        results.append({
                            'account_id': account.id,
                            'username': account.username,
                            'success': success
                        })
                        
                    except Exception as e:
                        results.append({
                            'account_id': account.id,
                            'username': account.username,
                            'success': False,
                            'error': str(e)
                        })
                
                # 清理浏览器池
                await browser_pool.close_all()
            
            # 运行异步登录
            loop.run_until_complete(login_accounts())
            
        except Exception as e:
            # 如果整个过程失败，为所有账号添加失败记录
            for i, account in enumerate(self.accounts):
                if i >= len(results):  # 只为未处理的账号添加失败记录
                    results.append({
                        'account_id': account.id,
                        'username': account.username,
                        'success': False,
                        'error': str(e)
                    })
        finally:
            loop.close()
        
        self.finished.emit(results)
    
    def stop(self):
        """停止线程"""
        self.running = False


class ConcurrentAccountLoginThread(QThread):
    """并发账号登录线程"""

    progress_updated = pyqtSignal(int, int, str, str)  # current, total, account_name, status
    finished = pyqtSignal(list)  # results

    def __init__(self, accounts: List[Account], max_concurrent: int = 3):
        super().__init__()
        self.accounts = accounts
        self.max_concurrent = max_concurrent
        self.running = True

    def stop(self):
        """停止登录"""
        self.running = False

    def run(self):
        """执行并发登录"""
        import asyncio
        from src.modules.posting.executor import LoginManager
        from src.core.browser_manager import get_browser_pool

        results = []
        total = len(self.accounts)

        # 创建异步事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # 使用并发LoginManager进行登录
            async def concurrent_login_accounts():
                # 初始化浏览器池
                browser_pool = get_browser_pool()
                await browser_pool.initialize()

                # 创建信号量控制并发数
                semaphore = asyncio.Semaphore(self.max_concurrent)
                completed_count = 0

                async def login_single_account(account):
                    nonlocal completed_count

                    async with semaphore:
                        if not self.running:
                            return {
                                'account_id': account.id,
                                'username': account.username,
                                'success': False,
                                'error': '用户取消'
                            }

                        try:
                            # 更新进度
                            self.progress_updated.emit(completed_count, total, account.username, "登录中")

                            # 执行登录
                            login_manager = LoginManager()
                            success = await login_manager.login_account(account)

                            completed_count += 1

                            # 更新进度
                            status = "成功" if success else "失败"
                            self.progress_updated.emit(completed_count, total, account.username, status)

                            return {
                                'account_id': account.id,
                                'username': account.username,
                                'success': success
                            }

                        except Exception as e:
                            completed_count += 1
                            self.progress_updated.emit(completed_count, total, account.username, "失败")

                            return {
                                'account_id': account.id,
                                'username': account.username,
                                'success': False,
                                'error': str(e)
                            }

                # 创建并发任务
                tasks = [login_single_account(account) for account in self.accounts]

                # 执行所有并发任务
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # 处理异常结果
                processed_results = []
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        processed_results.append({
                            'account_id': self.accounts[i].id,
                            'username': self.accounts[i].username,
                            'success': False,
                            'error': str(result)
                        })
                    else:
                        processed_results.append(result)

                # 清理浏览器池
                await browser_pool.close_all()

                return processed_results

            # 运行并发登录
            results = loop.run_until_complete(concurrent_login_accounts())

        except Exception as e:
            # 如果整个过程失败，为所有账号添加失败记录
            results = []
            for account in self.accounts:
                results.append({
                    'account_id': account.id,
                    'username': account.username,
                    'success': False,
                    'error': str(e)
                })

        finally:
            loop.close()
            self.finished.emit(results)


class AccountImportThread(QThread):
    """账号导入线程"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    import_completed = pyqtSignal(int, list)  # 成功数量, 错误列表
    
    def __init__(self, file_path: str, delimiter: str, group_id: int = None):
        super().__init__()
        self.file_path = file_path
        self.delimiter = delimiter
        self.group_id = group_id
        self.account_manager = AccountManager()
    
    def run(self):
        """执行导入"""
        try:
            self.status_updated.emit("正在导入账号...")
            self.progress_updated.emit(0)
            
            # 执行导入
            success_count, errors = self.account_manager.import_accounts(
                self.file_path, self.delimiter, self.group_id
            )
            
            self.progress_updated.emit(100)
            self.import_completed.emit(success_count, errors)
            
        except Exception as e:
            self.status_updated.emit(f"导入失败: {e}")
            self.import_completed.emit(0, [str(e)])


class AccountWidget(QWidget, LoggerMixin):
    """账号管理组件"""
    
    status_updated = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.account_manager = AccountManager()
        self.current_accounts = []

        self.init_ui()

        # 延迟加载数据，确保数据库已初始化
        QTimer.singleShot(100, self.load_data)

        # 设置定时刷新
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(30000)  # 30秒刷新一次
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 创建顶部控制面板
        top_panel = self.create_top_control_panel()
        layout.addWidget(top_panel)

        # 创建主要内容区域
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：筛选和操作面板
        left_panel = self.create_left_panel()
        main_splitter.addWidget(left_panel)

        # 右侧：账号表格
        right_panel = self.create_right_panel()
        main_splitter.addWidget(right_panel)

        # 设置分割器比例 (左侧280px固定, 右侧自适应)
        main_splitter.setSizes([280, 720])
        main_splitter.setStretchFactor(0, 0)  # 左侧不拉伸
        main_splitter.setStretchFactor(1, 1)  # 右侧可拉伸
        layout.addWidget(main_splitter)

        # 创建底部统计面板
        stats_panel = self.create_stats_panel()
        layout.addWidget(stats_panel)
    
    def create_top_control_panel(self) -> QGroupBox:
        """创建顶部控制面板 - 主要操作按钮"""
        group_box = QGroupBox("🛠️ 主要操作")
        group_box.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #0078d4;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: #f0f8ff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #0078d4;
            }
        """)
        layout = QHBoxLayout(group_box)

        # 主要操作按钮
        self.import_btn = QPushButton("📥 导入账号")
        self.import_btn.setStyleSheet(self.get_button_style("#0078d4"))
        self.import_btn.clicked.connect(self.import_accounts)
        layout.addWidget(self.import_btn)

        self.add_account_btn = QPushButton("➕ 新建账号")
        self.add_account_btn.setStyleSheet(self.get_button_style("#28a745"))
        self.add_account_btn.clicked.connect(self.add_new_account)
        layout.addWidget(self.add_account_btn)

        self.manage_groups_btn = QPushButton("📂 分组管理")
        self.manage_groups_btn.setStyleSheet(self.get_button_style("#6c757d"))
        self.manage_groups_btn.clicked.connect(self.manage_groups)
        layout.addWidget(self.manage_groups_btn)

        self.check_status_btn = QPushButton("🔍 检测状态")
        self.check_status_btn.setStyleSheet(self.get_button_style("#17a2b8"))
        self.check_status_btn.clicked.connect(self.check_selected_accounts_status)
        layout.addWidget(self.check_status_btn)

        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.setStyleSheet(self.get_button_style("#6c757d"))
        self.refresh_btn.clicked.connect(self.refresh_data)
        layout.addWidget(self.refresh_btn)

        layout.addStretch()

        return group_box

    def create_left_panel(self) -> QWidget:
        """创建左侧面板 - 筛选和批量操作"""
        widget = QWidget()
        widget.setMaximumWidth(280)  # 限制最大宽度
        layout = QVBoxLayout(widget)
        layout.setSpacing(8)
        layout.setContentsMargins(5, 5, 5, 5)

        # 筛选区域 - 使用紧凑布局
        filter_group = QGroupBox("🔍 筛选条件")
        filter_group.setCheckable(True)
        filter_group.setChecked(True)
        filter_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #28a745;
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #f8fff9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px 0 4px;
                color: #28a745;
                font-size: 12px;
            }
        """)
        filter_layout = QVBoxLayout(filter_group)
        filter_layout.setSpacing(6)
        filter_layout.setContentsMargins(8, 8, 8, 8)

        # 搜索框 - 紧凑设计
        search_container = QHBoxLayout()
        search_container.setSpacing(4)

        search_label = QLabel("搜索:")
        search_label.setStyleSheet("font-weight: bold; color: #495057; font-size: 11px;")
        search_label.setMinimumWidth(35)
        search_container.addWidget(search_label)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("用户名/邮箱...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 3px;
                background-color: white;
                font-size: 11px;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
        """)
        self.search_edit.textChanged.connect(self.filter_accounts)
        search_container.addWidget(self.search_edit)

        # 清除按钮
        clear_btn = QPushButton("×")
        clear_btn.setMaximumWidth(25)
        clear_btn.setMaximumHeight(25)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                font-weight: bold;
                color: #6c757d;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                color: #495057;
            }
        """)
        clear_btn.clicked.connect(lambda: self.search_edit.clear())
        search_container.addWidget(clear_btn)

        filter_layout.addLayout(search_container)

        # 分组和状态筛选 - 使用网格布局
        filter_grid = QGridLayout()
        filter_grid.setSpacing(4)

        # 分组筛选
        group_label = QLabel("分组:")
        group_label.setStyleSheet("font-weight: bold; color: #495057; font-size: 11px;")
        filter_grid.addWidget(group_label, 0, 0)

        self.group_combo = QComboBox()
        self.group_combo.setStyleSheet("""
            QComboBox {
                padding: 4px 6px;
                border: 1px solid #ced4da;
                border-radius: 3px;
                background-color: white;
                font-size: 11px;
                min-height: 20px;
            }
            QComboBox:hover {
                border-color: #0078d4;
            }
            QComboBox::drop-down {
                width: 20px;
            }
        """)
        self.load_groups()
        self.group_combo.currentTextChanged.connect(self.filter_accounts)
        filter_grid.addWidget(self.group_combo, 0, 1)

        # 状态筛选
        status_label = QLabel("状态:")
        status_label.setStyleSheet("font-weight: bold; color: #495057; font-size: 11px;")
        filter_grid.addWidget(status_label, 1, 0)

        self.status_combo = QComboBox()
        self.status_combo.addItem("全部", "")
        self.status_combo.addItem("未登录", ACCOUNT_STATUS['NOT_LOGGED_IN'])
        self.status_combo.addItem("已登录", ACCOUNT_STATUS['LOGGED_IN'])
        self.status_combo.addItem("已锁定", ACCOUNT_STATUS['LOCKED'])
        self.status_combo.setStyleSheet("""
            QComboBox {
                padding: 4px 6px;
                border: 1px solid #ced4da;
                border-radius: 3px;
                background-color: white;
                font-size: 11px;
                min-height: 20px;
            }
            QComboBox:hover {
                border-color: #0078d4;
            }
            QComboBox::drop-down {
                width: 20px;
            }
        """)
        self.status_combo.currentTextChanged.connect(self.filter_accounts)
        filter_grid.addWidget(self.status_combo, 1, 1)

        filter_layout.addLayout(filter_grid)
        layout.addWidget(filter_group)

        # 批量操作区域 - 简洁设计
        batch_group = QGroupBox("⚡ 批量登录")
        batch_group.setCheckable(True)
        batch_group.setChecked(True)
        batch_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #0078d4;
                border-radius: 6px;
                margin-top: 8px;
                padding-top: 12px;
                background-color: #f0f8ff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 6px 0 6px;
                color: #0078d4;
                font-size: 13px;
                font-weight: bold;
            }
        """)
        batch_layout = QVBoxLayout(batch_group)
        batch_layout.setSpacing(6)
        batch_layout.setContentsMargins(10, 8, 10, 8)

        # 主要登录按钮区域 - 改为水平布局
        login_buttons_layout = QHBoxLayout()
        login_buttons_layout.setSpacing(8)

        # 并发登录按钮 - 主要功能
        self.concurrent_login_btn = QPushButton("⚡ 并发登录")
        self.concurrent_login_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: 500;
                font-size: 12px;
                min-height: 24px;
                max-height: 28px;
                min-width: 80px;
                max-width: 90px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """)
        self.concurrent_login_btn.clicked.connect(self.concurrent_login_accounts)
        login_buttons_layout.addWidget(self.concurrent_login_btn)

        # 串行登录按钮 - 备选功能
        self.batch_login_btn = QPushButton("🔑 串行登录")
        self.batch_login_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: 500;
                font-size: 12px;
                min-height: 24px;
                max-height: 28px;
                min-width: 80px;
                max-width: 90px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        self.batch_login_btn.clicked.connect(self.batch_login_accounts)
        login_buttons_layout.addWidget(self.batch_login_btn)

        # 添加弹性空间，让按钮居左对齐
        login_buttons_layout.addStretch()

        batch_layout.addLayout(login_buttons_layout)

        # 登录配置区域 - 优化布局
        config_container = QWidget()
        config_container.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                border: 1px solid #e9ecef;
                border-radius: 5px;
                margin-top: 8px;
            }
        """)
        config_layout = QVBoxLayout(config_container)
        config_layout.setSpacing(8)
        config_layout.setContentsMargins(12, 8, 12, 8)

        # 配置标题
        config_title = QLabel("🔧 登录配置")
        config_title.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #495057;
                border: none;
                background: transparent;
                padding: 0 0 5px 0;
                margin-bottom: 5px;
                border-bottom: 1px solid #e9ecef;
            }
        """)
        config_layout.addWidget(config_title)

        # 配置项容器 - 使用网格布局让配置项更整齐
        config_grid = QGridLayout()
        config_grid.setSpacing(8)
        config_grid.setColumnStretch(1, 0)  # 不拉伸，保持紧凑
        config_grid.setColumnStretch(3, 0)  # 不拉伸，保持紧凑

        # 并发数配置
        concurrent_label = QLabel("并发数量:")
        concurrent_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #495057;
                font-weight: 500;
                border: none;
                background: transparent;
                padding: 0;
                min-width: 60px;
                max-width: 65px;
            }
        """)
        config_grid.addWidget(concurrent_label, 0, 0)

        self.concurrent_count_spin = QSpinBox()
        self.concurrent_count_spin.setRange(1, 10)
        self.concurrent_count_spin.setValue(3)
        self.concurrent_count_spin.setToolTip("同时登录的账号数量\n建议配置:\n• 1-3个账号: 1-2并发\n• 4-10个账号: 3-5并发\n• 10+个账号: 5-8并发")
        self.concurrent_count_spin.setStyleSheet("""
            QSpinBox {
                padding: 4px 6px;
                border: 1px solid #ced4da;
                border-radius: 3px;
                font-size: 11px;
                min-height: 20px;
                max-height: 24px;
                min-width: 60px;
                max-width: 70px;
                background-color: white;
            }
            QSpinBox:focus {
                border-color: #0078d4;
                outline: none;
            }
        """)
        config_grid.addWidget(self.concurrent_count_spin, 0, 1)

        # 登录模式配置
        mode_label = QLabel("登录模式:")
        mode_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #495057;
                font-weight: 500;
                border: none;
                background: transparent;
                padding: 0;
                min-width: 70px;
            }
        """)
        config_grid.addWidget(mode_label, 0, 2)

        self.login_mode_combo = QComboBox()
        self.login_mode_combo.addItem("🚀 并发模式", "concurrent")
        self.login_mode_combo.addItem("🔄 串行模式", "sequential")
        self.login_mode_combo.setCurrentIndex(0)  # 默认并发
        self.login_mode_combo.setToolTip("并发模式: 同时登录多个账号，速度快\n串行模式: 逐个登录账号，稳定性高")
        self.login_mode_combo.setStyleSheet("""
            QComboBox {
                padding: 4px 6px;
                border: 1px solid #ced4da;
                border-radius: 3px;
                font-size: 11px;
                min-height: 20px;
                max-height: 24px;
                min-width: 100px;
                max-width: 110px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #0078d4;
                outline: none;
            }
            QComboBox::drop-down {
                width: 18px;
                border: none;
            }
            QComboBox::down-arrow {
                width: 10px;
                height: 10px;
            }
        """)
        config_grid.addWidget(self.login_mode_combo, 0, 3)

        config_layout.addLayout(config_grid)

        # 添加提示信息
        tip_label = QLabel("💡 提示：并发登录速度更快，串行登录更稳定")
        tip_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #6c757d;
                font-style: italic;
                border: none;
                background: transparent;
                padding: 5px 0 0 0;
                margin-top: 5px;
                border-top: 1px solid #f1f3f4;
            }
        """)
        config_layout.addWidget(tip_label)

        batch_layout.addWidget(config_container)
        layout.addWidget(batch_group)

        layout.addStretch()

        return widget

    def create_right_panel(self) -> QWidget:
        """创建右侧面板 - 账号表格"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 表格标题
        table_label = QLabel("📋 账号列表")
        table_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #495057; margin-bottom: 5px;")
        layout.addWidget(table_label)

        # 创建账号表格
        self.create_account_table()
        layout.addWidget(self.account_table)

        return widget



    def get_compact_button_style(self, color: str = "#0078d4") -> str:
        """获取紧凑按钮样式"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                font-weight: bold;
                font-size: 10px;
                min-width: 60px;
                max-height: 24px;
            }}
            QPushButton:hover {{
                background-color: {self._darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self._darken_color(color, 0.2)};
            }}
        """

    def _darken_color(self, color: str, factor: float = 0.1) -> str:
        """使颜色变暗"""
        if color.startswith('#'):
            color = color[1:]

        # 转换为RGB
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)

        # 变暗
        r = max(0, int(r * (1 - factor)))
        g = max(0, int(g * (1 - factor)))
        b = max(0, int(b * (1 - factor)))

        return f"#{r:02x}{g:02x}{b:02x}"

    def get_button_style(self, color: str = "#0078d4") -> str:
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.2)};
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #666666;
            }}
        """

    def get_small_button_style(self) -> str:
        """获取小按钮样式"""
        return """
            QPushButton {
                background-color: #f8f9fa;
                color: #495057;
                border: 1px solid #dee2e6;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
                min-width: 50px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """

    def darken_color(self, color: str, factor: float = 0.1) -> str:
        """使颜色变暗"""
        # 简单的颜色变暗实现
        if color.startswith('#'):
            hex_color = color[1:]
            rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
            darkened = tuple(max(0, int(c * (1 - factor))) for c in rgb)
            return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"
        return color
    
    def create_account_table(self):
        """创建账号表格"""
        self.account_table = QTableWidget()
        self.account_table.setColumnCount(12)
        self.account_table.setHorizontalHeaderLabels([
            "ID", "用户名", "邮箱", "分组", "状态", "代理", "2FA", "Token",
            "Cookie", "发帖数", "最后登录", "最后活跃"
        ])

        # 设置表格属性
        self.account_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.account_table.setAlternatingRowColors(True)
        self.account_table.setSortingEnabled(True)

        # 允许多选
        self.account_table.setSelectionMode(QTableWidget.SelectionMode.ExtendedSelection)

        # 设置表格样式
        self.account_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #0078d4;
                selection-color: white;
                border: 1px solid #dee2e6;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableWidget::item:selected {
                background-color: #0078d4;
                color: white;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                color: #495057;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
            QHeaderView::section:hover {
                background-color: #e9ecef;
            }
        """)

        # 设置列宽
        header = self.account_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)  # 用户名
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)  # 邮箱
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 分组
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 状态
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 代理
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # 2FA
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)  # Token
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.ResizeToContents)  # Cookie
        header.setSectionResizeMode(9, QHeaderView.ResizeMode.ResizeToContents)  # 发帖数
        header.setSectionResizeMode(10, QHeaderView.ResizeMode.ResizeToContents)  # 最后登录
        header.setSectionResizeMode(11, QHeaderView.ResizeMode.Stretch)  # 最后活跃

        # 设置固定列宽，确保用户名和邮箱列始终可见
        header.resizeSection(1, 120)  # 用户名列宽度120px
        header.resizeSection(2, 180)  # 邮箱列宽度180px

        # 设置右键菜单
        self.account_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.account_table.customContextMenuRequested.connect(self.show_context_menu)

        # 双击编辑
        self.account_table.itemDoubleClicked.connect(self.edit_account_details)
    
    def create_stats_panel(self) -> QGroupBox:
        """创建统计面板"""
        group_box = QGroupBox("📊 统计信息")
        group_box.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #28a745;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: #f8fff9;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #28a745;
            }
        """)
        layout = QHBoxLayout(group_box)

        # 基础统计
        self.total_label = QLabel("📊 总数: 0")
        self.total_label.setStyleSheet("font-weight: bold; color: #495057; padding: 5px;")
        layout.addWidget(self.total_label)

        self.logged_in_label = QLabel("✅ 已登录: 0")
        self.logged_in_label.setStyleSheet("font-weight: bold; color: #28a745; padding: 5px;")
        layout.addWidget(self.logged_in_label)

        self.locked_label = QLabel("🔒 已锁定: 0")
        self.locked_label.setStyleSheet("font-weight: bold; color: #dc3545; padding: 5px;")
        layout.addWidget(self.locked_label)

        # 分隔线
        separator1 = QLabel("|")
        separator1.setStyleSheet("color: #ccc; font-size: 16px;")
        layout.addWidget(separator1)

        # 功能统计
        self.with_2fa_label = QLabel("🔐 有2FA: 0")
        self.with_2fa_label.setStyleSheet("font-weight: bold; color: #17a2b8; padding: 5px;")
        layout.addWidget(self.with_2fa_label)

        self.with_token_label = QLabel("🎫 有Token: 0")
        self.with_token_label.setStyleSheet("font-weight: bold; color: #6f42c1; padding: 5px;")
        layout.addWidget(self.with_token_label)

        self.with_proxy_label = QLabel("🌐 有代理: 0")
        self.with_proxy_label.setStyleSheet("font-weight: bold; color: #fd7e14; padding: 5px;")
        layout.addWidget(self.with_proxy_label)

        # 分隔线
        separator2 = QLabel("|")
        separator2.setStyleSheet("color: #ccc; font-size: 16px;")
        layout.addWidget(separator2)

        # 选择统计
        self.selected_label = QLabel("✔️ 已选择: 0")
        self.selected_label.setStyleSheet("font-weight: bold; color: #0078d4; padding: 5px; background-color: #e3f2fd; border-radius: 3px;")
        layout.addWidget(self.selected_label)

        layout.addStretch()

        # 连接选择改变事件
        self.account_table.itemSelectionChanged.connect(self.update_selection_stats)

        return group_box
    
    def load_groups(self):
        """加载分组列表"""
        try:
            self.logger.debug("开始加载分组列表...")

            self.group_combo.clear()
            self.group_combo.addItem("选择分组", None)

            groups = self.account_manager.group_manager.get_all_groups()
            self.logger.debug(f"获取到 {len(groups)} 个分组")

            for group in groups:
                self.group_combo.addItem(group.name, group.id)

        except Exception as e:
            self.logger.error(f"加载分组失败: {e}")
            # 确保至少有一个默认选项
            if self.group_combo.count() == 0:
                self.group_combo.addItem("选择分组", None)
    
    def load_data(self):
        """加载账号数据"""
        try:
            self.logger.debug("开始加载账号数据...")

            # 获取筛选条件
            group_id = self.group_combo.currentData()
            status = self.status_combo.currentData()

            self.logger.debug(f"筛选条件 - 分组ID: {group_id}, 状态: {status}")

            # 获取账号列表
            accounts, total_count = self.account_manager.get_accounts(
                group_id=group_id if group_id else None,
                status=status if status else None,
                page_size=1000  # 暂时加载所有数据
            )

            self.logger.debug(f"获取到 {len(accounts)} 个账号，总数: {total_count}")

            self.current_accounts = accounts
            self.update_table()
            self.update_statistics()

            self.status_updated.emit(f"已加载 {total_count} 个账号")

        except Exception as e:
            self.logger.error(f"加载账号数据失败: {e}")
            self.status_updated.emit(f"加载数据失败: {e}")
            # 显示错误对话框
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "加载失败", f"加载账号数据失败: {e}")
    
    def update_table(self):
        """更新表格显示"""
        try:
            # 应用搜索过滤
            search_text = self.search_edit.text().lower()
            filtered_accounts = []
            
            for account in self.current_accounts:
                if not search_text or search_text in account.username.lower():
                    filtered_accounts.append(account)
            
            # 更新表格
            self.account_table.setRowCount(len(filtered_accounts))
            
            for row, account in enumerate(filtered_accounts):
                # ID
                self.account_table.setItem(row, 0, QTableWidgetItem(str(account.id)))

                # 用户名
                username_item = QTableWidgetItem(account.username)
                username_item.setFont(QFont("", 0, QFont.Weight.Bold))
                self.account_table.setItem(row, 1, username_item)

                # 邮箱
                email = account.email or ""
                self.account_table.setItem(row, 2, QTableWidgetItem(email))

                # 分组
                group_name = account.group.name if account.group else "未分组"
                group_item = QTableWidgetItem(group_name)
                if account.group:
                    group_item.setBackground(self.get_group_color(account.group.type))
                self.account_table.setItem(row, 3, group_item)

                # 状态
                status_item = QTableWidgetItem(self.get_status_text(account.status))
                status_item.setBackground(self.get_status_color(account.status))
                self.account_table.setItem(row, 4, status_item)

                # 代理
                proxy_info = f"{account.proxy.ip}:{account.proxy.port}" if account.proxy else "无"
                proxy_item = QTableWidgetItem(proxy_info)
                if account.proxy:
                    proxy_item.setBackground(QColor(200, 255, 200))  # 浅绿色表示有代理
                self.account_table.setItem(row, 5, proxy_item)

                # 2FA状态
                tfa_status = "✓" if account.two_fa_secret else "✗"
                tfa_item = QTableWidgetItem(tfa_status)
                tfa_item.setBackground(QColor(200, 255, 200) if account.two_fa_secret else QColor(255, 200, 200))
                self.account_table.setItem(row, 6, tfa_item)

                # Token状态
                token_status = "✓" if account.token else "✗"
                token_item = QTableWidgetItem(token_status)
                token_item.setBackground(QColor(200, 255, 200) if account.token else QColor(255, 200, 200))
                self.account_table.setItem(row, 7, token_item)

                # Cookie状态
                cookie_status = "✓" if account.cookies else "✗"
                cookie_item = QTableWidgetItem(cookie_status)
                cookie_item.setBackground(QColor(200, 255, 200) if account.cookies else QColor(255, 200, 200))
                self.account_table.setItem(row, 8, cookie_item)

                # 发帖数
                post_count_item = QTableWidgetItem(str(account.post_count))
                if account.post_count > 0:
                    post_count_item.setBackground(QColor(255, 255, 200))  # 浅黄色表示有发帖
                self.account_table.setItem(row, 9, post_count_item)

                # 最后登录
                last_login = account.last_login_time.strftime("%Y-%m-%d %H:%M") if account.last_login_time else "从未"
                self.account_table.setItem(row, 10, QTableWidgetItem(last_login))

                # 最后活跃
                last_active = account.last_active_time.strftime("%Y-%m-%d %H:%M") if account.last_active_time else "从未"
                self.account_table.setItem(row, 11, QTableWidgetItem(last_active))
            
        except Exception as e:
            self.logger.error(f"更新表格失败: {e}")
    
    def update_statistics(self):
        """更新统计信息"""
        try:
            total = len(self.current_accounts)
            logged_in = sum(1 for acc in self.current_accounts if acc.status == ACCOUNT_STATUS['LOGGED_IN'])
            locked = sum(1 for acc in self.current_accounts if acc.status == ACCOUNT_STATUS['LOCKED'])

            # 功能统计
            with_2fa = sum(1 for acc in self.current_accounts if acc.two_fa_secret)
            with_token = sum(1 for acc in self.current_accounts if acc.token)
            with_proxy = sum(1 for acc in self.current_accounts if acc.proxy)

            # 更新标签
            self.total_label.setText(f"总数: {total}")
            self.logged_in_label.setText(f"已登录: {logged_in}")
            self.locked_label.setText(f"已锁定: {locked}")
            self.with_2fa_label.setText(f"有2FA: {with_2fa}")
            self.with_token_label.setText(f"有Token: {with_token}")
            self.with_proxy_label.setText(f"有代理: {with_proxy}")

        except Exception as e:
            self.logger.error(f"更新统计失败: {e}")

    def update_selection_stats(self):
        """更新选择统计"""
        try:
            selected_count = self.get_selected_account_count()
            self.selected_label.setText(f"已选择: {selected_count}")

        except Exception as e:
            self.logger.error(f"更新选择统计失败: {e}")
    
    def get_status_text(self, status: str) -> str:
        """获取状态文本"""
        status_map = {
            ACCOUNT_STATUS['NOT_LOGGED_IN']: "未登录",
            ACCOUNT_STATUS['LOGGED_IN']: "已登录",
            ACCOUNT_STATUS['LOCKED']: "已锁定"
        }
        return status_map.get(status, status)
    
    def get_status_color(self, status: str) -> QColor:
        """获取状态颜色"""
        color_map = {
            ACCOUNT_STATUS['NOT_LOGGED_IN']: QColor(255, 255, 255),  # 白色
            ACCOUNT_STATUS['LOGGED_IN']: QColor(144, 238, 144),      # 浅绿色
            ACCOUNT_STATUS['LOCKED']: QColor(255, 182, 193),         # 浅红色
            ACCOUNT_STATUS['SUSPENDED']: QColor(255, 165, 0),        # 橙色
            ACCOUNT_STATUS['RATE_LIMITED']: QColor(255, 255, 0)      # 黄色
        }
        return color_map.get(status, QColor(255, 255, 255))

    def get_group_color(self, group_type: str) -> QColor:
        """获取分组颜色"""
        from src.config.constants import GROUP_TYPES
        color_map = {
            GROUP_TYPES['POSTING']: QColor(173, 216, 230),      # 浅蓝色
            GROUP_TYPES['INTERACTION']: QColor(144, 238, 144),  # 浅绿色
            GROUP_TYPES['POSTED']: QColor(255, 182, 193),       # 浅红色
            GROUP_TYPES['CUSTOM']: QColor(221, 160, 221)        # 浅紫色
        }
        return color_map.get(group_type, QColor(255, 255, 255))

    def edit_account_details(self, item):
        """编辑账号详情"""
        try:
            row = item.row()
            if row >= len(self.current_accounts):
                return

            account = self.current_accounts[row]

            # 打开账号编辑对话框
            dialog = AccountEditDialog(account, self)
            dialog.account_updated.connect(self.on_account_updated)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"编辑账号详情失败: {e}")
            QMessageBox.critical(self, "错误", f"编辑账号详情失败: {e}")

    def add_new_account(self):
        """添加新账号"""
        try:
            # 打开新建账号对话框
            dialog = AccountEditDialog(None, self)
            dialog.account_updated.connect(self.on_account_updated)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"添加新账号失败: {e}")
            QMessageBox.critical(self, "错误", f"添加新账号失败: {e}")

    def on_account_updated(self):
        """账号更新回调"""
        self.refresh_data()

    def get_selected_accounts(self):
        """获取选中的账号列表"""
        try:
            selected_rows = set()
            for item in self.account_table.selectedItems():
                selected_rows.add(item.row())

            selected_accounts = []
            for row in selected_rows:
                if row < len(self.current_accounts):
                    selected_accounts.append(self.current_accounts[row])

            return selected_accounts
        except Exception as e:
            self.logger.error(f"获取选中账号失败: {e}")
            return []

    def check_selected_accounts_status(self):
        """检测选中账号的状态"""
        try:
            selected_rows = set()
            for item in self.account_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                QMessageBox.warning(self, "警告", "请先选择要检测的账号")
                return

            selected_accounts = []
            for row in selected_rows:
                if row < len(self.current_accounts):
                    selected_accounts.append(self.current_accounts[row])

            if not selected_accounts:
                return

            # 确认操作
            reply = QMessageBox.question(
                self, "确认检测",
                f"确定要检测选中的 {len(selected_accounts)} 个账号的状态吗？\n\n"
                "此操作可能需要一些时间，请耐心等待。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 创建进度对话框
            progress_dialog = QProgressDialog("正在检测账号状态...", "取消", 0, len(selected_accounts), self)
            progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
            progress_dialog.show()

            # 这里应该创建异步任务来检测状态
            # 暂时模拟检测过程
            for i, account in enumerate(selected_accounts):
                if progress_dialog.wasCanceled():
                    break

                progress_dialog.setValue(i)
                progress_dialog.setLabelText(f"正在检测 {account.username}...")

                # 模拟检测延时
                import time
                time.sleep(0.5)

            progress_dialog.setValue(len(selected_accounts))
            progress_dialog.close()

            QMessageBox.information(self, "完成", f"已完成 {len(selected_accounts)} 个账号的状态检测")
            self.refresh_data()

        except Exception as e:
            self.logger.error(f"检测账号状态失败: {e}")
            QMessageBox.critical(self, "错误", f"检测账号状态失败: {e}")

    def check_all_accounts_status(self):
        """检测所有账号状态"""
        try:
            if not self.current_accounts:
                QMessageBox.information(self, "提示", "没有账号需要检测")
                return

            # 确认操作
            reply = QMessageBox.question(
                self, "确认检测",
                f"确定要检测所有 {len(self.current_accounts)} 个账号的状态吗？\n\n"
                "此操作可能需要较长时间，请耐心等待。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 创建并启动状态检测线程
            self.status_check_thread = AccountStatusCheckThread(self.current_accounts)
            self.status_check_thread.progress_updated.connect(self.on_status_check_progress)
            self.status_check_thread.finished.connect(self.on_status_check_finished)
            self.status_check_thread.start()

            # 显示进度对话框
            self.progress_dialog = QProgressDialog("正在检测账号状态...", "取消", 0, len(self.current_accounts), self)
            self.progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
            self.progress_dialog.canceled.connect(self.status_check_thread.stop)
            self.progress_dialog.show()

        except Exception as e:
            self.logger.error(f"检测所有账号状态失败: {e}")
            QMessageBox.critical(self, "错误", f"检测所有账号状态失败: {e}")

    def batch_login_accounts(self):
        """批量登录账号"""
        try:
            # 获取选中的账号
            selected_accounts = self.get_selected_accounts()
            if not selected_accounts:
                QMessageBox.information(self, "提示", "请先选择要登录的账号")
                return

            # 过滤出未登录的账号
            not_logged_in_accounts = [acc for acc in selected_accounts if acc.status == 'not_logged_in']
            if not not_logged_in_accounts:
                QMessageBox.information(self, "提示", "选中的账号中没有需要登录的账号")
                return

            # 确认操作
            reply = QMessageBox.question(
                self, "确认登录",
                f"确定要登录选中的 {len(not_logged_in_accounts)} 个账号吗？\n\n"
                "此操作可能需要较长时间，请耐心等待。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 创建并启动登录线程
            self.login_thread = AccountLoginThread(not_logged_in_accounts)
            self.login_thread.progress_updated.connect(self.on_login_progress)
            self.login_thread.finished.connect(self.on_login_finished)
            self.login_thread.start()

            # 显示进度对话框
            self.progress_dialog = QProgressDialog("正在登录账号...", "取消", 0, len(not_logged_in_accounts), self)
            self.progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
            self.progress_dialog.canceled.connect(self.login_thread.stop)
            self.progress_dialog.show()

        except Exception as e:
            self.logger.error(f"批量登录账号失败: {e}")
            QMessageBox.critical(self, "错误", f"批量登录账号失败: {e}")

    def concurrent_login_accounts(self):
        """并发登录账号"""
        try:
            # 获取选中的账号
            selected_accounts = self.get_selected_accounts()
            if not selected_accounts:
                QMessageBox.information(self, "提示", "请先选择要登录的账号")
                return

            # 过滤出未登录的账号
            not_logged_in_accounts = [acc for acc in selected_accounts if acc.status == 'not_logged_in']
            if not not_logged_in_accounts:
                QMessageBox.information(self, "提示", "选中的账号中没有需要登录的账号")
                return

            # 获取并发配置
            max_concurrent = self.concurrent_count_spin.value()
            login_mode = self.login_mode_combo.currentData()

            # 确认操作
            mode_text = "并发" if login_mode == "concurrent" else "串行"
            concurrent_info = f"(最大并发: {max_concurrent})" if login_mode == "concurrent" else ""

            reply = QMessageBox.question(
                self, "确认登录",
                f"确定要{mode_text}登录选中的 {len(not_logged_in_accounts)} 个账号吗？{concurrent_info}\n\n"
                f"并发登录可以大幅提升效率，但会占用更多系统资源。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 根据模式选择登录方式
            if login_mode == "concurrent":
                # 创建并启动并发登录线程
                self.concurrent_login_thread = ConcurrentAccountLoginThread(
                    not_logged_in_accounts, max_concurrent
                )
                self.concurrent_login_thread.progress_updated.connect(self.on_login_progress)
                self.concurrent_login_thread.finished.connect(self.on_login_finished)
                self.concurrent_login_thread.start()

                # 显示进度对话框
                self.progress_dialog = QProgressDialog(
                    f"正在并发登录账号... (最大并发: {max_concurrent})",
                    "取消", 0, len(not_logged_in_accounts), self
                )
                self.progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
                self.progress_dialog.canceled.connect(self.concurrent_login_thread.stop)
                self.progress_dialog.show()
            else:
                # 使用串行登录
                self.batch_login_accounts()

        except Exception as e:
            self.logger.error(f"并发登录账号失败: {e}")
            QMessageBox.critical(self, "错误", f"并发登录账号失败: {e}")

    def on_status_check_progress(self, current, total, account_name, status):
        """状态检测进度更新"""
        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.setValue(current)
            self.progress_dialog.setLabelText(f"正在检测账号状态... ({current}/{total})\n当前: {account_name}")

    def on_status_check_finished(self, results):
        """状态检测完成"""
        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.close()

        success_count = sum(1 for result in results if result['success'])
        total_count = len(results)

        # 刷新账号列表
        self.refresh_accounts()

        QMessageBox.information(
            self, "检测完成",
            f"状态检测完成！\n\n"
            f"成功检测: {success_count}/{total_count} 个账号"
        )

    def on_login_progress(self, current, total, account_name, status):
        """登录进度更新"""
        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.setValue(current)
            self.progress_dialog.setLabelText(f"正在登录账号... ({current}/{total})\n当前: {account_name}")

    def on_login_finished(self, results):
        """登录完成"""
        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.close()

        success_count = sum(1 for result in results if result['success'])
        total_count = len(results)

        # 刷新账号列表
        self.refresh_accounts()

        QMessageBox.information(
            self, "登录完成",
            f"批量登录完成！\n\n"
            f"成功登录: {success_count}/{total_count} 个账号"
        )
    
    def filter_accounts(self):
        """筛选账号"""
        self.load_data()
    
    def refresh_accounts(self):
        """刷新账号列表"""
        self.load_data()
        self.status_updated.emit("账号列表已刷新")
    
    def refresh_data(self):
        """刷新数据"""
        self.load_groups()
        self.load_data()
        self.status_updated.emit("账号数据已刷新")
    
    def import_accounts(self):
        """导入账号"""
        try:
            # 打开导入对话框
            dialog = AccountImportDialog(self)
            dialog.import_completed.connect(self.on_import_completed)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.refresh_data()

        except Exception as e:
            self.logger.error(f"打开导入对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"打开导入对话框失败: {e}")

    def manage_groups(self):
        """管理分组"""
        try:
            # 打开分组管理对话框
            dialog = GroupManagementDialog(self)
            dialog.group_updated.connect(self.on_group_updated)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"打开分组管理对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"打开分组管理对话框失败: {e}")

    def on_group_updated(self):
        """分组更新回调"""
        self.load_groups()
        self.refresh_data()
    
    def select_delimiter(self) -> str:
        """选择分隔符"""
        from PyQt6.QtWidgets import QInputDialog
        
        delimiters = ["----", "|||", "\t", ",", "|"]
        delimiter, ok = QInputDialog.getItem(
            self, "选择分隔符", "请选择账号文件的分隔符:", delimiters, 0, False
        )
        
        return delimiter if ok else None
    
    def on_import_completed(self, success_count: int, errors: list):
        """导入完成回调"""
        try:
            if success_count > 0:
                QMessageBox.information(
                    self, "导入完成", 
                    f"成功导入 {success_count} 个账号"
                )
                self.refresh_data()
            
            if errors:
                error_text = "\n".join(errors[:10])  # 只显示前10个错误
                if len(errors) > 10:
                    error_text += f"\n... 还有 {len(errors) - 10} 个错误"
                
                QMessageBox.warning(self, "导入警告", f"导入过程中出现错误:\n{error_text}")
            
        except Exception as e:
            self.logger.error(f"处理导入结果失败: {e}")
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.account_table.itemAt(position) is None:
            return

        selected_rows = set()
        for item in self.account_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            return

        menu = QMenu(self)

        # 根据选择数量显示不同菜单
        if len(selected_rows) == 1:
            # 单个账号操作
            edit_action = QAction("编辑账号", self)
            edit_action.triggered.connect(lambda: self.edit_account_details(self.account_table.itemAt(position)))
            menu.addAction(edit_action)

            menu.addSeparator()

        # 批量操作
        if len(selected_rows) > 1:
            batch_menu = menu.addMenu(f"批量操作 ({len(selected_rows)}个账号)")

            # 批量移动到分组
            move_to_group_menu = batch_menu.addMenu("移动到分组")
            groups = self.account_manager.group_manager.get_all_groups()
            for group in groups:
                action = QAction(group.name, self)
                action.triggered.connect(lambda checked, g=group: self.move_to_group(g.id))
                move_to_group_menu.addAction(action)

            # 批量状态更新
            status_menu = batch_menu.addMenu("更新状态")
            status_actions = [
                ("设为未登录", ACCOUNT_STATUS['NOT_LOGGED_IN']),
                ("设为已登录", ACCOUNT_STATUS['LOGGED_IN']),
                ("设为已锁定", ACCOUNT_STATUS['LOCKED'])
            ]
            for status_text, status_value in status_actions:
                action = QAction(status_text, self)
                action.triggered.connect(lambda checked, s=status_value: self.batch_update_status(s))
                status_menu.addAction(action)

            batch_menu.addSeparator()

            # 批量状态检测
            check_status_action = QAction("检测状态", self)
            check_status_action.triggered.connect(self.check_selected_accounts_status)
            batch_menu.addAction(check_status_action)

            batch_menu.addSeparator()

            # 批量删除
            delete_action = QAction("批量删除", self)
            delete_action.triggered.connect(self.delete_selected_accounts)
            batch_menu.addAction(delete_action)
        else:
            # 单个账号操作
            # 移动到分组
            move_to_group_menu = menu.addMenu("移动到分组")
            groups = self.account_manager.group_manager.get_all_groups()
            for group in groups:
                action = QAction(group.name, self)
                action.triggered.connect(lambda checked, g=group: self.move_to_group(g.id))
                move_to_group_menu.addAction(action)

            menu.addSeparator()

            # 删除账号
            delete_action = QAction("删除账号", self)
            delete_action.triggered.connect(self.delete_selected_accounts)
            menu.addAction(delete_action)

        menu.exec(self.account_table.mapToGlobal(position))
    
    def move_to_group(self, group_id: int):
        """移动选中账号到分组"""
        try:
            selected_rows = set()
            for item in self.account_table.selectedItems():
                selected_rows.add(item.row())
            
            if not selected_rows:
                return
            
            account_ids = []
            for row in selected_rows:
                account_id = int(self.account_table.item(row, 0).text())
                account_ids.append(account_id)
            
            success_count = self.account_manager.move_accounts_to_group(account_ids, group_id)
            
            if success_count > 0:
                QMessageBox.information(self, "成功", f"成功移动 {success_count} 个账号")
                self.refresh_data()
            
        except Exception as e:
            self.logger.error(f"移动账号失败: {e}")
            QMessageBox.critical(self, "错误", f"移动账号失败: {e}")
    
    def delete_selected_accounts(self):
        """删除选中的账号"""
        try:
            selected_rows = set()
            for item in self.account_table.selectedItems():
                selected_rows.add(item.row())
            
            if not selected_rows:
                return
            
            # 确认删除
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除选中的 {len(selected_rows)} 个账号吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply != QMessageBox.StandardButton.Yes:
                return
            
            account_ids = []
            for row in selected_rows:
                account_id = int(self.account_table.item(row, 0).text())
                account_ids.append(account_id)
            
            success_count = self.account_manager.delete_accounts(account_ids)
            
            if success_count > 0:
                QMessageBox.information(self, "成功", f"成功删除 {success_count} 个账号")
                self.refresh_data()
            
        except Exception as e:
            self.logger.error(f"删除账号失败: {e}")
            QMessageBox.critical(self, "错误", f"删除账号失败: {e}")

    def batch_update_status(self, new_status: str):
        """批量更新账号状态"""
        try:
            selected_rows = set()
            for item in self.account_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                return

            # 确认操作
            status_text = self.get_status_text(new_status)
            reply = QMessageBox.question(
                self, "确认操作",
                f"确定要将选中的 {len(selected_rows)} 个账号状态设为 '{status_text}' 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            account_ids = []
            for row in selected_rows:
                account_id = int(self.account_table.item(row, 0).text())
                account_ids.append(account_id)

            # 这里应该调用账号管理器的批量更新状态方法
            # 暂时模拟成功
            success_count = len(account_ids)

            if success_count > 0:
                QMessageBox.information(self, "成功", f"成功更新 {success_count} 个账号状态")
                self.refresh_data()

        except Exception as e:
            self.logger.error(f"批量更新状态失败: {e}")
            QMessageBox.critical(self, "错误", f"批量更新状态失败: {e}")

    def get_selected_account_count(self) -> int:
        """获取选中账号数量"""
        selected_rows = set()
        for item in self.account_table.selectedItems():
            selected_rows.add(item.row())
        return len(selected_rows)

    def select_all_accounts(self):
        """全选账号"""
        try:
            self.account_table.selectAll()

        except Exception as e:
            self.logger.error(f"全选账号失败: {e}")

    def select_none_accounts(self):
        """取消选择所有账号"""
        try:
            self.account_table.clearSelection()

        except Exception as e:
            self.logger.error(f"取消选择失败: {e}")

    def invert_selection(self):
        """反选账号"""
        try:
            # 获取当前选中的行
            selected_rows = set()
            for item in self.account_table.selectedItems():
                selected_rows.add(item.row())

            # 清除当前选择
            self.account_table.clearSelection()

            # 选择未选中的行
            for row in range(self.account_table.rowCount()):
                if row not in selected_rows:
                    self.account_table.selectRow(row)

        except Exception as e:
            self.logger.error(f"反选失败: {e}")

    def select_by_status(self, status: str):
        """按状态选择账号"""
        try:
            self.account_table.clearSelection()

            for row in range(self.account_table.rowCount()):
                if row < len(self.current_accounts):
                    account = self.current_accounts[row]
                    if account.status == status:
                        self.account_table.selectRow(row)

        except Exception as e:
            self.logger.error(f"按状态选择失败: {e}")

    def select_by_group(self, group_id: int):
        """按分组选择账号"""
        try:
            self.account_table.clearSelection()

            for row in range(self.account_table.rowCount()):
                if row < len(self.current_accounts):
                    account = self.current_accounts[row]
                    if (group_id is None and account.group_id is None) or \
                       (account.group_id == group_id):
                        self.account_table.selectRow(row)

        except Exception as e:
            self.logger.error(f"按分组选择失败: {e}")
